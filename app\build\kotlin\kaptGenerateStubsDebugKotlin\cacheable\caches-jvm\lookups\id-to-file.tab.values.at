/ Header Record For PersistentHashMapValueStorageB Aapp/src/main/java/com/example/castapp/websocket/ControlMessage.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowVisualizationContainerView.kt@ ?app/src/main/java/com/example/castapp/ui/view/TextWindowView.ktL Kapp/src/main/java/com/example/castapp/remote/RemoteReceiverControlServer.ktE Dapp/src/main/java/com/example/castapp/utils/WindowScaleCalculator.ktG Fapp/src/main/java/com/example/castapp/ui/view/PrecisionControlPanel.ktJ Iapp/src/main/java/com/example/castapp/ui/view/CropVisualizationOverlay.ktG Fapp/src/main/java/com/example/castapp/ui/dialog/WindowManagerDialog.ktD Capp/src/main/java/com/example/castapp/ui/dialog/SaveLayoutDialog.ktF Eapp/src/main/java/com/example/castapp/ui/dialog/LayerManagerDialog.ktB Aapp/src/main/java/com/example/castapp/ui/dialog/DirectorDialog.ktI Happ/src/main/java/com/example/castapp/ui/adapter/WindowManagerAdapter.ktH Gapp/src/main/java/com/example/castapp/ui/adapter/LayerManagerAdapter.ktS Rapp/src/main/java/com/example/castapp/manager/windowsettings/WindowLayoutModule.ktQ Papp/src/main/java/com/example/castapp/manager/windowsettings/WindowInfoModule.ktS Rapp/src/main/java/com/example/castapp/manager/windowsettings/WindowDialogModule.ktG Fapp/src/main/java/com/example/castapp/manager/WindowSettingsManager.ktN Mapp/src/main/java/com/example/castapp/manager/PrecisionControlPanelManager.kt? >app/src/main/java/com/example/castapp/manager/LayoutManager.ktP Oapp/src/main/java/com/example/castapp/database/entity/WindowLayoutItemEntity.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktM Lapp/src/main/java/com/example/castapp/ui/dialog/RemoteWindowManagerDialog.ktO Napp/src/main/java/com/example/castapp/ui/dialog/RemoteReceiverControlDialog.ktG Fapp/src/main/java/com/example/castapp/model/WindowVisualizationData.kt> =app/src/main/java/com/example/castapp/model/CastWindowInfo.ktG Fapp/src/main/java/com/example/castapp/manager/RemoteReceiverManager.ktV Uapp/src/main/java/com/example/castapp/manager/windowsettings/WindowOperationModule.ktQ Papp/src/main/java/com/example/castapp/manager/windowsettings/WindowDataModule.ktM Lapp/src/main/java/com/example/castapp/ui/dialog/LineSpacingSettingsDialog.ktO Napp/src/main/java/com/example/castapp/ui/dialog/LetterSpacingSettingsDialog.ktJ Iapp/src/main/java/com/example/castapp/ui/dialog/FontSizeSettingsDialog.ktF Eapp/src/main/java/com/example/castapp/ui/dialog/FontSettingsDialog.ktH Gapp/src/main/java/com/example/castapp/ui/dialog/FontFilePickerDialog.ktH Gapp/src/main/java/com/example/castapp/ui/adapter/LayoutDetailAdapter.ktN Mapp/src/main/java/com/example/castapp/ui/adapter/CustomColorPaletteAdapter.ktL Kapp/src/main/java/com/example/castapp/ui/windowsettings/TransformHandler.ktL Kapp/src/main/java/com/example/castapp/ui/windowsettings/TransformManager.ktG Fapp/src/main/java/com/example/castapp/ui/windowsettings/CropManager.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TransformRenderer.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/ScreenshotManager.ktN Mapp/src/main/java/com/example/castapp/ui/adapter/RemoteSenderDeviceAdapter.ktH Gapp/src/main/java/com/example/castapp/ui/adapter/RemoteSenderAdapter.ktP Oapp/src/main/java/com/example/castapp/ui/adapter/RemoteReceiverDeviceAdapter.ktF Eapp/src/main/java/com/example/castapp/ui/adapter/LayoutListAdapter.ktF Eapp/src/main/java/com/example/castapp/ui/adapter/ConnectionAdapter.kt@ ?app/src/main/java/com/example/castapp/model/WindowUpdateMode.ktA @app/src/main/java/com/example/castapp/utils/TextFormatManager.kt9 8app/src/main/java/com/example/castapp/ui/MainActivity.ktU Tapp/src/main/java/com/example/castapp/manager/windowsettings/WindowCreationModule.ktF Eapp/src/main/java/com/example/castapp/database/dao/WindowLayoutDao.ktB Aapp/src/main/java/com/example/castapp/database/CastAppDatabase.kt? >app/src/main/java/com/example/castapp/ui/view/TextEditPanel.kt? >app/src/main/java/com/example/castapp/utils/LineSpacingSpan.ktH Gapp/src/main/java/com/example/castapp/utils/LineSpacingPresetManager.ktJ Iapp/src/main/java/com/example/castapp/utils/LetterSpacingPresetManager.ktA @app/src/main/java/com/example/castapp/utils/LetterSpacingSpan.ktA @app/src/main/java/com/example/castapp/utils/FontPresetManager.ktC Bapp/src/main/java/com/example/castapp/audio/AudioCaptureManager.kt< ;app/src/main/java/com/example/castapp/audio/AudioEncoder.kt; :app/src/main/java/com/example/castapp/audio/AudioPlayer.kt< ;app/src/main/java/com/example/castapp/codec/VideoDecoder.kt< ;app/src/main/java/com/example/castapp/codec/VideoEncoder.ktJ Iapp/src/main/java/com/example/castapp/ui/windowsettings/SurfaceManager.ktD Capp/src/main/java/com/example/castapp/manager/MultiCameraManager.ktC Bapp/src/main/java/com/example/castapp/manager/PermissionManager.kt@ ?app/src/main/java/com/example/castapp/service/CastingService.ktM Lapp/src/main/java/com/example/castapp/ui/dialog/RemoteSenderControlDialog.ktW Vapp/src/main/java/com/example/castapp/ui/dialog/RemoteReceiverSettingsControlDialog.ktB Aapp/src/main/java/com/example/castapp/ui/dialog/NoteEditDialog.ktE Dapp/src/main/java/com/example/castapp/ui/dialog/ColorPickerDialog.ktJ Iapp/src/main/java/com/example/castapp/ui/dialog/AddMediaDialogFragment.ktE Dapp/src/main/java/com/example/castapp/ui/view/ResizableBorderView.ktC Bapp/src/main/java/com/example/castapp/websocket/WebSocketServer.ktC Bapp/src/main/java/com/example/castapp/websocket/WebSocketClient.ktC Bapp/src/main/java/com/example/castapp/viewmodel/SenderViewModel.ktE Dapp/src/main/java/com/example/castapp/viewmodel/ReceiverViewModel.ktA @app/src/main/java/com/example/castapp/viewmodel/MainViewModel.kt: 9app/src/main/java/com/example/castapp/utils/ToastUtils.kt? >app/src/main/java/com/example/castapp/utils/TextSizeManager.kt: 9app/src/main/java/com/example/castapp/utils/StrokeSpan.kt? >app/src/main/java/com/example/castapp/utils/ResourceManager.ktC Bapp/src/main/java/com/example/castapp/utils/PeriodicCleanupTask.ktC Bapp/src/main/java/com/example/castapp/utils/NotificationManager.kt; :app/src/main/java/com/example/castapp/utils/NoteManager.kt= <app/src/main/java/com/example/castapp/utils/MemoryMonitor.kt@ ?app/src/main/java/com/example/castapp/utils/MediaFileManager.ktE Dapp/src/main/java/com/example/castapp/utils/FontSizePresetManager.kt; :app/src/main/java/com/example/castapp/utils/DeviceUtils.kt: 9app/src/main/java/com/example/castapp/utils/ColorUtils.ktC Bapp/src/main/java/com/example/castapp/utils/ColorPaletteManager.kt6 5app/src/main/java/com/example/castapp/utils/AppLog.kt] \app/src/main/java/com/example/castapp/ui/windowsettings/interfaces/TransformStateListener.ktX Wapp/src/main/java/com/example/castapp/ui/windowsettings/interfaces/CropStateListener.ktQ Papp/src/main/java/com/example/castapp/ui/windowsettings/WindowPositionManager.ktO Napp/src/main/java/com/example/castapp/ui/windowsettings/MediaSurfaceManager.ktD Capp/src/main/java/com/example/castapp/ui/view/GestureOverlayView.ktA @app/src/main/java/com/example/castapp/ui/view/CropOverlayView.ktQ Papp/src/main/java/com/example/castapp/ui/helper/LayoutItemTouchHelperCallback.ktM Lapp/src/main/java/com/example/castapp/ui/fragment/RemoteSenderTabFragment.ktO Napp/src/main/java/com/example/castapp/ui/fragment/RemoteReceiverTabFragment.ktE Dapp/src/main/java/com/example/castapp/ui/dialog/SaveOptionsDialog.ktN Mapp/src/main/java/com/example/castapp/ui/dialog/RemoteControlManagerDialog.ktP Oapp/src/main/java/com/example/castapp/ui/dialog/EditRemoteSenderDeviceDialog.ktR Qapp/src/main/java/com/example/castapp/ui/dialog/EditRemoteReceiverDeviceDialog.ktD Capp/src/main/java/com/example/castapp/ui/dialog/EditLayoutDialog.ktO Napp/src/main/java/com/example/castapp/ui/dialog/AddRemoteSenderDeviceDialog.ktQ Papp/src/main/java/com/example/castapp/ui/dialog/AddRemoteReceiverDeviceDialog.ktJ Iapp/src/main/java/com/example/castapp/ui/adapter/RemoteTabPagerAdapter.kt< ;app/src/main/java/com/example/castapp/ui/StopwatchWindow.ktA @app/src/main/java/com/example/castapp/ui/SenderDialogFragment.ktC Bapp/src/main/java/com/example/castapp/ui/ReceiverDialogFragment.ktG Fapp/src/main/java/com/example/castapp/service/RemoteReceiverService.ktB Aapp/src/main/java/com/example/castapp/service/ReceivingService.ktJ Iapp/src/main/java/com/example/castapp/service/FloatingStopwatchService.kt> =app/src/main/java/com/example/castapp/service/AudioService.kt7 6app/src/main/java/com/example/castapp/rtp/RtpSender.kt9 8app/src/main/java/com/example/castapp/rtp/RtpReceiver.kt7 6app/src/main/java/com/example/castapp/rtp/RtpPacket.kt9 8app/src/main/java/com/example/castapp/rtp/PayloadView.ktD Capp/src/main/java/com/example/castapp/rtp/MultiConnectionManager.ktL Kapp/src/main/java/com/example/castapp/remote/RemoteSenderWebSocketClient.ktC Bapp/src/main/java/com/example/castapp/remote/RemoteSenderServer.kt; :app/src/main/java/com/example/castapp/network/UdpSender.kt= <app/src/main/java/com/example/castapp/network/UdpReceiver.ktD Capp/src/main/java/com/example/castapp/network/SmartBufferManager.kt> =app/src/main/java/com/example/castapp/network/NetworkUtils.ktF Eapp/src/main/java/com/example/castapp/model/RemoteSenderConnection.ktH Gapp/src/main/java/com/example/castapp/model/RemoteReceiverConnection.kt: 9app/src/main/java/com/example/castapp/model/Connection.ktV Uapp/src/main/java/com/example/castapp/manager/windowsettings/WindowLifecycleModule.ktB Aapp/src/main/java/com/example/castapp/manager/WebSocketManager.ktG Fapp/src/main/java/com/example/castapp/manager/VideoReceivingManager.kt> =app/src/main/java/com/example/castapp/manager/StateManager.ktC Bapp/src/main/java/com/example/castapp/manager/ResolutionManager.ktE Dapp/src/main/java/com/example/castapp/manager/RemoteSenderManager.ktI Happ/src/main/java/com/example/castapp/manager/RemoteConnectionManager.ktC Bapp/src/main/java/com/example/castapp/manager/MicrophoneManager.ktI Happ/src/main/java/com/example/castapp/manager/MessageReceivingManager.ktH Gapp/src/main/java/com/example/castapp/manager/MediaProjectionManager.ktA @app/src/main/java/com/example/castapp/manager/HideShowManager.ktG Fapp/src/main/java/com/example/castapp/manager/FloatingWindowManager.ktG Fapp/src/main/java/com/example/castapp/manager/AudioReceivingManager.ktL Kapp/src/main/java/com/example/castapp/database/entity/WindowLayoutEntity.ktJ Iapp/src/main/java/com/example/castapp/database/converter/DateConverter.kt@ ?app/src/main/java/com/example/castapp/audio/AudioSyncManager.kt> =app/src/main/java/com/example/castapp/audio/AudioRtpSender.kt@ ?app/src/main/java/com/example/castapp/audio/AudioRtpReceiver.kt< ;app/src/main/java/com/example/castapp/audio/AudioDecoder.kt? >app/src/main/java/com/example/castapp/audio/AudioBufferPool.ktO Napp/src/main/java/com/example/castapp/ui/dialog/RemoteReceiverControlDialog.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowVisualizationContainerView.ktA @app/src/main/java/com/example/castapp/utils/TextFormatManager.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowVisualizationContainerView.ktQ Papp/src/main/java/com/example/castapp/manager/windowsettings/WindowInfoModule.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowVisualizationContainerView.ktG Fapp/src/main/java/com/example/castapp/manager/RemoteReceiverManager.ktG Fapp/src/main/java/com/example/castapp/manager/WindowSettingsManager.ktQ Papp/src/main/java/com/example/castapp/manager/windowsettings/WindowInfoModule.kt> =app/src/main/java/com/example/castapp/model/CastWindowInfo.ktG Fapp/src/main/java/com/example/castapp/model/WindowVisualizationData.ktL Kapp/src/main/java/com/example/castapp/remote/RemoteReceiverControlServer.ktO Napp/src/main/java/com/example/castapp/ui/dialog/RemoteReceiverControlDialog.ktM Lapp/src/main/java/com/example/castapp/ui/dialog/RemoteWindowManagerDialog.kt@ ?app/src/main/java/com/example/castapp/ui/view/TextWindowView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowVisualizationContainerView.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktA @app/src/main/java/com/example/castapp/utils/TextFormatManager.ktB Aapp/src/main/java/com/example/castapp/websocket/ControlMessage.ktP Oapp/src/main/java/com/example/castapp/database/entity/WindowLayoutItemEntity.kt? >app/src/main/java/com/example/castapp/manager/LayoutManager.ktN Mapp/src/main/java/com/example/castapp/manager/PrecisionControlPanelManager.ktS Rapp/src/main/java/com/example/castapp/manager/windowsettings/WindowDialogModule.ktS Rapp/src/main/java/com/example/castapp/manager/windowsettings/WindowLayoutModule.ktH Gapp/src/main/java/com/example/castapp/ui/adapter/LayerManagerAdapter.ktI Happ/src/main/java/com/example/castapp/ui/adapter/WindowManagerAdapter.ktB Aapp/src/main/java/com/example/castapp/ui/dialog/DirectorDialog.ktF Eapp/src/main/java/com/example/castapp/ui/dialog/LayerManagerDialog.ktD Capp/src/main/java/com/example/castapp/ui/dialog/SaveLayoutDialog.ktG Fapp/src/main/java/com/example/castapp/ui/dialog/WindowManagerDialog.ktJ Iapp/src/main/java/com/example/castapp/ui/view/CropVisualizationOverlay.ktG Fapp/src/main/java/com/example/castapp/ui/view/PrecisionControlPanel.ktE Dapp/src/main/java/com/example/castapp/utils/WindowScaleCalculator.ktG Fapp/src/main/java/com/example/castapp/manager/WindowSettingsManager.ktO Napp/src/main/java/com/example/castapp/ui/dialog/RemoteReceiverControlDialog.ktG Fapp/src/main/java/com/example/castapp/model/WindowVisualizationData.ktO Napp/src/main/java/com/example/castapp/ui/dialog/RemoteReceiverControlDialog.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowVisualizationContainerView.ktJ Iapp/src/main/java/com/example/castapp/ui/view/CropVisualizationOverlay.ktE Dapp/src/main/java/com/example/castapp/utils/WindowScaleCalculator.ktG Fapp/src/main/java/com/example/castapp/model/WindowVisualizationData.ktO Napp/src/main/java/com/example/castapp/ui/dialog/RemoteReceiverControlDialog.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowVisualizationContainerView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowVisualizationContainerView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowVisualizationContainerView.ktO Napp/src/main/java/com/example/castapp/ui/dialog/RemoteReceiverControlDialog.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowVisualizationContainerView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowVisualizationContainerView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowVisualizationContainerView.ktO Napp/src/main/java/com/example/castapp/ui/dialog/RemoteReceiverControlDialog.ktO Napp/src/main/java/com/example/castapp/ui/dialog/RemoteReceiverControlDialog.ktO Napp/src/main/java/com/example/castapp/ui/dialog/RemoteReceiverControlDialog.ktG Fapp/src/main/java/com/example/castapp/model/WindowVisualizationData.ktE Dapp/src/main/java/com/example/castapp/utils/WindowScaleCalculator.ktO Napp/src/main/java/com/example/castapp/ui/dialog/RemoteReceiverControlDialog.ktJ Iapp/src/main/java/com/example/castapp/ui/view/CropVisualizationOverlay.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowVisualizationContainerView.ktG Fapp/src/main/java/com/example/castapp/model/WindowVisualizationData.ktE Dapp/src/main/java/com/example/castapp/utils/WindowScaleCalculator.ktO Napp/src/main/java/com/example/castapp/ui/dialog/RemoteReceiverControlDialog.ktJ Iapp/src/main/java/com/example/castapp/ui/view/CropVisualizationOverlay.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowVisualizationContainerView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowVisualizationContainerView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowVisualizationContainerView.ktL Kapp/src/main/java/com/example/castapp/ui/windowsettings/TransformManager.ktG Fapp/src/main/java/com/example/castapp/model/WindowVisualizationData.ktL Kapp/src/main/java/com/example/castapp/ui/windowsettings/TransformManager.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowVisualizationContainerView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowVisualizationContainerView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowVisualizationContainerView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowVisualizationContainerView.ktG Fapp/src/main/java/com/example/castapp/model/WindowVisualizationData.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktG Fapp/src/main/java/com/example/castapp/model/WindowVisualizationData.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowVisualizationContainerView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowVisualizationContainerView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowVisualizationContainerView.ktL Kapp/src/main/java/com/example/castapp/ui/windowsettings/TransformManager.ktL Kapp/src/main/java/com/example/castapp/ui/windowsettings/TransformHandler.ktL Kapp/src/main/java/com/example/castapp/ui/windowsettings/TransformManager.ktL Kapp/src/main/java/com/example/castapp/ui/windowsettings/TransformHandler.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktL Kapp/src/main/java/com/example/castapp/ui/windowsettings/TransformHandler.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktL Kapp/src/main/java/com/example/castapp/ui/windowsettings/TransformHandler.ktL Kapp/src/main/java/com/example/castapp/ui/windowsettings/TransformManager.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktL Kapp/src/main/java/com/example/castapp/ui/windowsettings/TransformManager.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowVisualizationContainerView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowVisualizationContainerView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TransformRenderer.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TransformRenderer.ktG Fapp/src/main/java/com/example/castapp/model/WindowVisualizationData.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowVisualizationContainerView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowVisualizationContainerView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktU Tapp/src/main/java/com/example/castapp/manager/windowsettings/WindowCreationModule.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TransformRenderer.ktU Tapp/src/main/java/com/example/castapp/manager/windowsettings/WindowCreationModule.ktS Rapp/src/main/java/com/example/castapp/manager/windowsettings/WindowLayoutModule.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TransformRenderer.ktL Kapp/src/main/java/com/example/castapp/ui/windowsettings/TransformHandler.ktO Napp/src/main/java/com/example/castapp/ui/dialog/RemoteReceiverControlDialog.kt> =app/src/main/java/com/example/castapp/model/CastWindowInfo.ktO Napp/src/main/java/com/example/castapp/ui/dialog/RemoteReceiverControlDialog.ktP Oapp/src/main/java/com/example/castapp/database/entity/WindowLayoutItemEntity.kt? >app/src/main/java/com/example/castapp/manager/LayoutManager.ktN Mapp/src/main/java/com/example/castapp/manager/PrecisionControlPanelManager.ktG Fapp/src/main/java/com/example/castapp/manager/WindowSettingsManager.ktS Rapp/src/main/java/com/example/castapp/manager/windowsettings/WindowDialogModule.ktQ Papp/src/main/java/com/example/castapp/manager/windowsettings/WindowInfoModule.ktS Rapp/src/main/java/com/example/castapp/manager/windowsettings/WindowLayoutModule.ktG Fapp/src/main/java/com/example/castapp/model/WindowVisualizationData.ktH Gapp/src/main/java/com/example/castapp/ui/adapter/LayerManagerAdapter.ktI Happ/src/main/java/com/example/castapp/ui/adapter/WindowManagerAdapter.ktB Aapp/src/main/java/com/example/castapp/ui/dialog/DirectorDialog.ktF Eapp/src/main/java/com/example/castapp/ui/dialog/LayerManagerDialog.ktM Lapp/src/main/java/com/example/castapp/ui/dialog/RemoteWindowManagerDialog.ktD Capp/src/main/java/com/example/castapp/ui/dialog/SaveLayoutDialog.ktG Fapp/src/main/java/com/example/castapp/ui/dialog/WindowManagerDialog.ktG Fapp/src/main/java/com/example/castapp/ui/view/PrecisionControlPanel.ktE Dapp/src/main/java/com/example/castapp/utils/WindowScaleCalculator.kt> =app/src/main/java/com/example/castapp/model/CastWindowInfo.ktO Napp/src/main/java/com/example/castapp/ui/dialog/RemoteReceiverControlDialog.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktG Fapp/src/main/java/com/example/castapp/manager/WindowSettingsManager.kt> =app/src/main/java/com/example/castapp/model/CastWindowInfo.ktG Fapp/src/main/java/com/example/castapp/model/WindowVisualizationData.ktO Napp/src/main/java/com/example/castapp/ui/dialog/RemoteReceiverControlDialog.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowVisualizationContainerView.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktF Eapp/src/main/java/com/example/castapp/utils/RemoteTextFormatParser.ktA @app/src/main/java/com/example/castapp/utils/TextFormatManager.ktP Oapp/src/main/java/com/example/castapp/database/entity/WindowLayoutItemEntity.kt? >app/src/main/java/com/example/castapp/manager/LayoutManager.ktN Mapp/src/main/java/com/example/castapp/manager/PrecisionControlPanelManager.ktS Rapp/src/main/java/com/example/castapp/manager/windowsettings/WindowDialogModule.ktQ Papp/src/main/java/com/example/castapp/manager/windowsettings/WindowInfoModule.ktS Rapp/src/main/java/com/example/castapp/manager/windowsettings/WindowLayoutModule.ktH Gapp/src/main/java/com/example/castapp/ui/adapter/LayerManagerAdapter.ktI Happ/src/main/java/com/example/castapp/ui/adapter/WindowManagerAdapter.ktB Aapp/src/main/java/com/example/castapp/ui/dialog/DirectorDialog.ktF Eapp/src/main/java/com/example/castapp/ui/dialog/LayerManagerDialog.ktM Lapp/src/main/java/com/example/castapp/ui/dialog/RemoteWindowManagerDialog.ktD Capp/src/main/java/com/example/castapp/ui/dialog/SaveLayoutDialog.ktG Fapp/src/main/java/com/example/castapp/ui/dialog/WindowManagerDialog.ktJ Iapp/src/main/java/com/example/castapp/ui/view/CropVisualizationOverlay.ktG Fapp/src/main/java/com/example/castapp/ui/view/PrecisionControlPanel.ktE Dapp/src/main/java/com/example/castapp/utils/WindowScaleCalculator.ktG Fapp/src/main/java/com/example/castapp/manager/WindowSettingsManager.ktN Mapp/src/main/java/com/example/castapp/ui/adapter/CustomColorPaletteAdapter.ktH Gapp/src/main/java/com/example/castapp/ui/adapter/LayerManagerAdapter.ktH Gapp/src/main/java/com/example/castapp/ui/adapter/LayoutDetailAdapter.ktF Eapp/src/main/java/com/example/castapp/ui/adapter/LayoutListAdapter.ktI Happ/src/main/java/com/example/castapp/ui/adapter/WindowManagerAdapter.ktH Gapp/src/main/java/com/example/castapp/ui/dialog/FontFilePickerDialog.ktF Eapp/src/main/java/com/example/castapp/ui/dialog/FontSettingsDialog.ktJ Iapp/src/main/java/com/example/castapp/ui/dialog/FontSizeSettingsDialog.ktO Napp/src/main/java/com/example/castapp/ui/dialog/LetterSpacingSettingsDialog.ktM Lapp/src/main/java/com/example/castapp/ui/dialog/LineSpacingSettingsDialog.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowVisualizationContainerView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowVisualizationContainerView.ktF Eapp/src/main/java/com/example/castapp/utils/RemoteTextFormatParser.ktG Fapp/src/main/java/com/example/castapp/model/WindowVisualizationData.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowVisualizationContainerView.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktF Eapp/src/main/java/com/example/castapp/utils/RemoteTextFormatParser.ktO Napp/src/main/java/com/example/castapp/ui/dialog/RemoteReceiverControlDialog.ktJ Iapp/src/main/java/com/example/castapp/ui/view/CropVisualizationOverlay.ktE Dapp/src/main/java/com/example/castapp/utils/WindowScaleCalculator.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowVisualizationContainerView.ktF Eapp/src/main/java/com/example/castapp/utils/RemoteTextFormatParser.ktF Eapp/src/main/java/com/example/castapp/utils/RemoteTextFormatParser.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktF Eapp/src/main/java/com/example/castapp/utils/RemoteTextFormatParser.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.kt@ ?app/src/main/java/com/example/castapp/ui/view/TextWindowView.ktA @app/src/main/java/com/example/castapp/utils/LetterSpacingSpan.ktA @app/src/main/java/com/example/castapp/utils/LetterSpacingSpan.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.kt