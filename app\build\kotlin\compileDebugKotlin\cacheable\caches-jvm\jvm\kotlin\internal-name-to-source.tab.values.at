c/main/java/com/example/castapp/viewmodel/SenderViewModel.ktC Bapp/src/main/java/com/example/castapp/viewmodel/SenderViewModel.ktC Bapp/src/main/java/com/example/castapp/viewmodel/SenderViewModel.ktC Bapp/src/main/java/com/example/castapp/viewmodel/SenderViewModel.ktC Bapp/src/main/java/com/example/castapp/viewmodel/SenderViewModel.ktC Bapp/src/main/java/com/example/castapp/viewmodel/SenderViewModel.ktC Bapp/src/main/java/com/example/castapp/viewmodel/SenderViewModel.ktB Aapp/src/main/java/com/example/castapp/websocket/ControlMessage.ktB Aapp/src/main/java/com/example/castapp/websocket/ControlMessage.ktC Bapp/src/main/java/com/example/castapp/websocket/WebSocketClient.ktC Bapp/src/main/java/com/example/castapp/websocket/WebSocketClient.ktC Bapp/src/main/java/com/example/castapp/websocket/WebSocketClient.ktC Bapp/src/main/java/com/example/castapp/websocket/WebSocketClient.ktC Bapp/src/main/java/com/example/castapp/websocket/WebSocketServer.ktC Bapp/src/main/java/com/example/castapp/websocket/WebSocketServer.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowVisualizationContainerView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowVisualizationContainerView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowVisualizationContainerView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowVisualizationContainerView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowVisualizationContainerView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowVisualizationContainerView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowVisualizationContainerView.ktF Eapp/src/main/java/com/example/castapp/utils/RemoteTextFormatParser.ktF Eapp/src/main/java/com/example/castapp/utils/RemoteTextFormatParser.ktG Fapp/src/main/java/com/example/castapp/model/WindowVisualizationData.ktG Fapp/src/main/java/com/example/castapp/model/WindowVisualizationData.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowVisualizationContainerView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowVisualizationContainerView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowVisualizationContainerView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowVisualizationContainerView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowVisualizationContainerView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowVisualizationContainerView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowVisualizationContainerView.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktF Eapp/src/main/java/com/example/castapp/utils/RemoteTextFormatParser.ktF Eapp/src/main/java/com/example/castapp/utils/RemoteTextFormatParser.ktO Napp/src/main/java/com/example/castapp/ui/dialog/RemoteReceiverControlDialog.ktO Napp/src/main/java/com/example/castapp/ui/dialog/RemoteReceiverControlDialog.ktO Napp/src/main/java/com/example/castapp/ui/dialog/RemoteReceiverControlDialog.ktO Napp/src/main/java/com/example/castapp/ui/dialog/RemoteReceiverControlDialog.ktO Napp/src/main/java/com/example/castapp/ui/dialog/RemoteReceiverControlDialog.ktO Napp/src/main/java/com/example/castapp/ui/dialog/RemoteReceiverControlDialog.ktO Napp/src/main/java/com/example/castapp/ui/dialog/RemoteReceiverControlDialog.ktO Napp/src/main/java/com/example/castapp/ui/dialog/RemoteReceiverControlDialog.ktO Napp/src/main/java/com/example/castapp/ui/dialog/RemoteReceiverControlDialog.ktO Napp/src/main/java/com/example/castapp/ui/dialog/RemoteReceiverControlDialog.ktJ Iapp/src/main/java/com/example/castapp/ui/view/CropVisualizationOverlay.ktJ Iapp/src/main/java/com/example/castapp/ui/view/CropVisualizationOverlay.ktJ Iapp/src/main/java/com/example/castapp/ui/view/CropVisualizationOverlay.ktJ Iapp/src/main/java/com/example/castapp/ui/view/CropVisualizationOverlay.ktE Dapp/src/main/java/com/example/castapp/utils/WindowScaleCalculator.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowVisualizationContainerView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowVisualizationContainerView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowVisualizationContainerView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowVisualizationContainerView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowVisualizationContainerView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowVisualizationContainerView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowVisualizationContainerView.ktF Eapp/src/main/java/com/example/castapp/utils/RemoteTextFormatParser.ktF Eapp/src/main/java/com/example/castapp/utils/RemoteTextFormatParser.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktF Eapp/src/main/java/com/example/castapp/utils/RemoteTextFormatParser.ktF Eapp/src/main/java/com/example/castapp/utils/RemoteTextFormatParser.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.kt@ ?app/src/main/java/com/example/castapp/ui/view/TextWindowView.kt@ ?app/src/main/java/com/example/castapp/ui/view/TextWindowView.kt@ ?app/src/main/java/com/example/castapp/ui/view/TextWindowView.kt@ ?app/src/main/java/com/example/castapp/ui/view/TextWindowView.kt@ ?app/src/main/java/com/example/castapp/ui/view/TextWindowView.ktA @app/src/main/java/com/example/castapp/utils/LetterSpacingSpan.ktS Rapp/src/main/java/com/example/castapp/manager/windowsettings/WindowLayoutModule.ktS Rapp/src/main/java/com/example/castapp/manager/windowsettings/WindowLayoutModule.ktA @app/src/main/java/com/example/castapp/utils/TextFormatManager.ktA @app/src/main/java/com/example/castapp/utils/TextFormatManager.ktA @app/src/main/java/com/example/castapp/utils/TextFormatManager.ktA @app/src/main/java/com/example/castapp/utils/TextFormatManager.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktM Lapp/src/main/java/com/example/castapp/ui/windowsettings/TextWindowManager.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.ktR Qapp/src/main/java/com/example/castapp/ui/view/WindowContainerVisualizationView.kt